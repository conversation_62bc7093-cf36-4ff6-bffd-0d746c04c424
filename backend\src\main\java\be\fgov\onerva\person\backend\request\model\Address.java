package be.fgov.onerva.person.backend.request.model;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.*;

@Embeddable
@Getter
@Builder
@ToString
@NoArgsConstructor(access = AccessLevel.PACKAGE)
@AllArgsConstructor(access = AccessLevel.PACKAGE)
public class Address {
  @NotBlank
  private String street;

  private String number;

  private String box;
  @Column(length = 10)
  private String zip;
  @NotBlank
  @Column(length = 35)
  private String city;
  @Min(1)
  @Max(999)
  private Integer countryCode;
}
