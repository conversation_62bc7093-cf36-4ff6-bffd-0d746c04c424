package be.fgov.onerva.person.backend.validation;

import be.fgov.onerva.person.backend.lookup.LookupClient;
import be.fgov.onerva.person.backend.lookup.model.LookupData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Country code validator that uses the LookupClient to verify country codes
 * against the CBSS nationality codes lookup service.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CountryCodeValidator {

    private final LookupClient lookupClient;

    /**
     * Validates a country code against the CBSS nationality codes lookup service.
     *
     * @param countryCode the country code to validate (can be null)
     * @return true if the country code is valid, false otherwise
     */
    public boolean isValid(Integer countryCode) {
        if (countryCode == null) {
            return false;
        }

        try {
            Set<String> validCodes = getValidCountryCodes();
            return validCodes.contains(countryCode.toString());
        } catch (Exception e) {
            log.error("Error validating country code {}: {}", countryCode, e.getMessage());
            return false;
        }
    }

    /**
     * Validates a country code and provides detailed error information.
     *
     * @param countryCode the country code to validate
     * @return ValidationResult containing validation status and error details
     */
    public ValidationResult validateWithDetails(Integer countryCode) {
        if (countryCode == null) {
            return new ValidationResult(false, "Country code cannot be null");
        }

        if (countryCode < 1 || countryCode > 999) {
            return new ValidationResult(false, "Country code must be between 1 and 999");
        }

        try {
            Set<String> validCodes = getValidCountryCodes();
            if (validCodes.contains(countryCode.toString())) {
                return new ValidationResult(true, "Country code is valid");
            } else {
                return new ValidationResult(false, 
                    String.format("Country code %d is not found in CBSS nationality codes", countryCode));
            }
        } catch (Exception e) {
            log.error("Error validating country code {}: {}", countryCode, e.getMessage());
            return new ValidationResult(false, 
                String.format("Error validating country code: %s", e.getMessage()));
        }
    }

    /**
     * Checks if a country code represents Belgium.
     * Belgium is typically represented by country code 1 in CBSS.
     *
     * @param countryCode the country code to check
     * @return true if the country code represents Belgium, false otherwise
     */
    public boolean isBelgium(Integer countryCode) {
        return countryCode != null && countryCode == 1;
    }

    /**
     * Checks if a country code represents a foreign country (non-Belgium).
     *
     * @param countryCode the country code to check
     * @return true if the country code represents a foreign country, false otherwise
     */
    public boolean isForeign(Integer countryCode) {
        return countryCode != null && countryCode != 1 && isValid(countryCode);
    }

    /**
     * Gets all valid country codes from the lookup service.
     * Results are cached to improve performance.
     *
     * @return Set of valid country code strings
     */
    @Cacheable("countryCodes")
    public Set<String> getValidCountryCodes() {
        log.debug("Fetching country codes from lookup service");
        List<LookupData> nationalityCodes = lookupClient.findAllNationalityCodes();
        
        Set<String> codes = nationalityCodes.stream()
                .map(LookupData::getCode)
                .collect(Collectors.toSet());
        
        log.debug("Retrieved {} country codes from lookup service", codes.size());
        return codes;
    }

    /**
     * Gets the description for a country code in the specified language.
     *
     * @param countryCode the country code
     * @param language the language ("fr" for French, "nl" for Dutch)
     * @return the country description or null if not found
     */
    public String getCountryDescription(Integer countryCode, String language) {
        if (countryCode == null) {
            return null;
        }

        try {
            List<LookupData> nationalityCodes = lookupClient.findAllNationalityCodes();
            return nationalityCodes.stream()
                    .filter(lookup -> lookup.getCode().equals(countryCode.toString()))
                    .findFirst()
                    .map(lookup -> "fr".equalsIgnoreCase(language) ? lookup.getDescFr() : lookup.getDescNl())
                    .orElse(null);
        } catch (Exception e) {
            log.error("Error getting country description for code {}: {}", countryCode, e.getMessage());
            return null;
        }
    }

    /**
     * Result of country code validation with detailed information.
     */
    public static class ValidationResult {
        private final boolean valid;
        private final String message;

        public ValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }

        public boolean isValid() {
            return valid;
        }

        public String getMessage() {
            return message;
        }

        @Override
        public String toString() {
            return String.format("ValidationResult{valid=%s, message='%s'}", valid, message);
        }
    }
}
