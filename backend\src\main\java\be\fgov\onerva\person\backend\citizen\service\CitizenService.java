package be.fgov.onerva.person.backend.citizen.service;

import be.fgov.onerva.common.utils.InssUtils;
import be.fgov.onerva.common.utils.PensionNumberUtils;
import be.fgov.onerva.person.backend.citizen.model.BusinessDomain;
import be.fgov.onerva.person.backend.citizen.model.CitizenCreationRequest;
import be.fgov.onerva.person.backend.citizen.model.CitizenEntity;
import be.fgov.onerva.person.backend.citizen.model.CitizenUpdateRequest;
import be.fgov.onerva.person.backend.citizen.persistence.CitizenRepository;
import be.fgov.onerva.person.backend.lookup.LookupClient;
import be.fgov.onerva.person.backend.request.model.PersonRequest;
import be.fgov.onerva.person.backend.request.service.PersonRequestService;
import be.fgov.onerva.person.backend.validation.CountryCodeValidator;
import be.fgov.onerva.person.backend.validation.IbanValidator;
import be.fgov.onerva.wave.api.UserApi;
import be.fgov.onerva.wave.model.User;
import be.fgov.onerva.wave.model.UserCriteria;
import io.github.perplexhub.rsql.RSQLJPASupport;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ProblemDetail;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.ErrorResponseException;
import org.springframework.web.client.HttpClientErrorException;

@Slf4j
@Service
@Validated
@RequiredArgsConstructor
public class CitizenService {

    private final CitizenRepository citizenRepository;
    private final PersonRequestService personRequestService;
    private final LookupClient lookupClient;
    private final UserApi userApi;
    private final CountryCodeValidator countryCodeValidator;
    private final IbanValidator ibanValidator;

    public CitizenEntity getByNiss(@NotNull String niss) {
        var numpens = PensionNumberUtils.convertFromInss(Long.parseLong(niss));
        if (numpens == null) {
            throw new CitizenNotFoundException();
        }
        return citizenRepository.findById(numpens).orElseThrow(CitizenNotFoundException::new);
    }

    public CitizenEntity getByNumbox(@NotNull Integer numbox) {
        // TODO quick fix ... should be further analyzed
        return citizenRepository.findByNumBox(numbox).stream()
                .filter(citizen -> citizen.getLastId() == 9)
                .findAny()
                .orElseThrow(CitizenNotFoundException::new);
    }

    public Page<CitizenEntity> searchCitizenByQuery(@NotNull String query, @NotNull Pageable pageable) {
        return citizenRepository.findAll(RSQLJPASupport.rsql(query), pageable);
    }

    public PersonRequest createCitizen(@Valid @NotNull CitizenCreationRequest request) {
        if (request.isAllowance()) {
            throw new UnsupportedOperationException("Only supported for citizens that are not asking an allowance.");
        }
        if (request.getDomain() != BusinessDomain.ADMISSIBILITY) {
            throw new UnsupportedOperationException("Only business domain ADMISSIBILITY supported for the moment.");
        }
        long inss = Long.parseLong(request.getNiss());
        if (!InssUtils.isValid(inss)) {
            throw new IllegalArgumentException("Invalid inss: " + request.getNiss());
        }
        var numpens = PensionNumberUtils.convertFromInss(inss);
        if (citizenRepository.existsById(numpens)) {
            throw new CitizenExistsException();
        }
        return personRequestService.createMinimalPersonInfo(request.getFirstname(), request.getLastname(),
                request.getNiss(), request.getCorrelationId());
    }

    public PersonRequest updateCitizen(@Valid @NotNull CitizenUpdateRequest request) {
        long inss = Long.parseLong(request.getNiss());
        if (!InssUtils.isValid(inss)) {
            throw new IllegalArgumentException("Invalid inss: " + request.getNiss());
        }
        var numpens = PensionNumberUtils.convertFromInss(inss);
        if (!citizenRepository.existsById(numpens)) {
            throw new CitizenNotFoundException();
        }
        // Validate nationality code using CountryCodeValidator
        if (request.getNationalityCode() != null) {
            if (!countryCodeValidator.isValid(request.getNationalityCode())) {
                throw new IllegalArgumentException("Invalid nationality code: " + request.getNationalityCode());
            }
        }

        // Validate address country code
        if (request.getAddress() != null && request.getAddress().getCountryCode() != null) {
            if (!countryCodeValidator.isValid(request.getAddress().getCountryCode())) {
                throw new IllegalArgumentException("Invalid country code: " + request.getAddress().getCountryCode());
            }
        }
        // Validate zip code based on country
        validateZipCode(request);

        // Validate language code (1=fr, 2=nl, 3=de)
        if (request.getLanguageCode() != null) {
            if (request.getLanguageCode() < 1 || request.getLanguageCode() > 3) {
                throw new IllegalArgumentException(
                        "Invalid language code: " + request.getLanguageCode() + ". Must be 1=fr, 2=nl, or 3=de");
            }
        }

        // Validate birth date format (YYYYMMDD)
        if (request.getBirthDate() != null) {
            if (!request.getBirthDate().matches("\\d{8}")) {
                throw new IllegalArgumentException(
                        "Invalid birth date format: " + request.getBirthDate() + ". Must be YYYYMMDD");
            }
        }

        // Validate bank information
        validateBankInfo(request);

        // Validate union due information
        validateUnionDueInfo(request);
        try {
            User user = userApi.searchUsers(new UserCriteria().username(request.getUsername()));

            return personRequestService.updatePersonInfo(request, user);
        } catch (HttpClientErrorException.Unauthorized | HttpClientErrorException.Forbidden e) {
            log.error("Not authorized to call the user endpoint.", e);
            throw new ErrorResponseException(
                    e.getStatusCode(),
                    ProblemDetail.forStatusAndDetail(e.getStatusCode(),
                            "Not authorized to call the user endpoint! Check client security."),
                    e);
        } catch (HttpClientErrorException e) {
            throw new IllegalArgumentException("Could not find user: " + request.getUsername(), e);
        }
    }

    /**
     * Validates zip code based on country code.
     * For Belgian addresses (country code 1), validates against Belgian zip codes.
     * For foreign addresses, validates format (up to 10 characters).
     */
    private void validateZipCode(CitizenUpdateRequest request) {
        if (request.getAddress() == null || request.getAddress().getZip() == null) {
            return;
        }

        String zip = request.getAddress().getZip();
        Integer countryCode = request.getAddress().getCountryCode();

        if (countryCodeValidator.isBelgium(countryCode)) {
            // Belgian zip code validation (4 digits)
            try {
                int zipCode = Integer.parseInt(zip);
                if (lookupClient.findBelgianZipCode(zipCode).isEmpty()) {
                    throw new IllegalArgumentException("Invalid Belgian zip code: " + zip);
                }
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("Invalid Belgian zip code format: " + zip + ". Must be 4 digits");
            }
        } else {
            // Foreign zip code validation (up to 10 characters)
            if (zip.length() > 10) {
                throw new IllegalArgumentException("Foreign zip code cannot exceed 10 characters: " + zip);
            }
        }
    }

    /**
     * Validates bank information including IBAN, BIC, and business rules.
     */
    private void validateBankInfo(CitizenUpdateRequest request) {
        // Check both new nested structure and deprecated fields for backward
        // compatibility
        String iban = null;
        String bic = null;
        Integer countryCode = request.getAddress() != null ? request.getAddress().getCountryCode() : null;

        if (request.getBankInfo() != null) {
            iban = request.getBankInfo().getIban();
            bic = request.getBankInfo().getBic();
        }

        // Validate IBAN if provided
        if (iban != null && !iban.trim().isEmpty()) {
            if (!ibanValidator.isValid(iban)) {
                throw new IllegalArgumentException("Invalid IBAN: " + iban);
            }
        }

        // BIC mandatory validation for non-Belgian IBANs
        if (iban != null && !iban.trim().isEmpty() && !countryCodeValidator.isBelgium(countryCode)) {
            if (bic == null || bic.trim().isEmpty()) {
                throw new IllegalArgumentException("BIC is mandatory for non-Belgian IBANs");
            }
        }
    }

    /**
     * Validates union due information.
     */
    private void validateUnionDueInfo(CitizenUpdateRequest request) {
        // Check both new nested structure and deprecated fields for backward
        // compatibility
        if (request.getUnionDueInfo() != null) {
            if (request.getUnionDueInfo().getUnionDue() == null) {
                throw new IllegalArgumentException("Union due status is required when union due info is provided");
            }
            if (request.getUnionDueInfo().getValueDate() == null) {
                throw new IllegalArgumentException("Value date is required for union due info");
            }
        }
        // Note: Deprecated unionDue field validation is handled by Jakarta validation
        // annotations
    }

    /**
     * Implements backward compatibility for deprecated fields.
     * Maps root-level paymentType/unionDue to nested objects if nested objects are
     * not provided.
     */
    private void handleBackwardCompatibility(CitizenUpdateRequest request) {
        // Handle backward compatibility for paymentType
        if (request.getBankInfo() == null && request.getPaymentType() != null) {
            // Create BankUpdateInfo from deprecated paymentType field
            // This would be implemented when the service layer is updated
        }

        // Handle backward compatibility for unionDue
        if (request.getUnionDueInfo() == null && request.getUnionDue() != null) {
            // Create UnionDueUpdateInfo from deprecated unionDue field
            // This would be implemented when the service layer is updated
        }
    }
}
